{"name": "treasure_hunt_on_my_own", "version": "1.0.0", "main": "index.js", "license": "MIT", "scripts": {"dev": "ts-node-dev ./src/app.ts", "clean": "rm -rf node_modules yarn.lock && yarn cache clean"}, "dependencies": {"@prisma/client": "6.16.1", "express": "^5.1.0", "typescript": "^5.9.2"}, "devDependencies": {"@types/express": "^5.0.3", "@types/node": "^24.3.3", "prisma": "^6.16.1", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0"}}