import { PrismaClient } from "../../generated/prisma";
import { Request, Response, Router } from "express";

const router = Router();
const prisma = new PrismaClient();

router.get("/", async (req: Request, res: Response) => {
  const hunts = await prisma.hunt.findMany();
  res.json(hunts);
});

router.get("/:id", async (req: Request, res: Response) => {
  const { id } = req.params;
  const hunt = await prisma.hunt.findUnique({
    where: { id: Number(id) },
  });
  res.json(hunt);
});

router.post("/", async (req: Request, res: Response) => {
  const { title } = req.body;
  if (!title) {
    throw new Error("title is required");
  }

  const result = await prisma.hunt.create({
    data: { title },
  });
  res.json(result);
});

router.patch("/:id", async (req: Request, res: Response) => {
  const { id } = req.params;
  if (!id) {
    throw new Error("id is required");
  }

  const { title } = req.body;
  if (!title) {
    throw new Error("title is required");
  }

  const hunt = await prisma.hunt.update({
    where: { id: Number(id) },
    data: { title },
  });
  res.json(hunt);
});

router.delete("/:id", async (req: Request, res: Response) => {
  const { id } = req.params;
  await prisma.hunt.delete({
    where: { id: Number(id) },
  });
  res.status(204).end();
});

export default router;
