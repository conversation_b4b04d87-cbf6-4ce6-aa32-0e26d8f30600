docker compose up -d

yarn prisma migrate dev --name init
yarn prisma migrate

docker compose exec postgres psql -U treasure_hunt_on_my_own -d treasure_hunt

curl http://localhost:3000/api/hunts
curl http://localhost:3000/api/hunts/1
curl -X POST http://localhost:3000/api/hunts -H "Content-Type: application/json" -d '{"title":"hunt #1"}'
curl -X PATCH http://localhost:3000/api/hunts/1 -H "Content-Type: application/json" -d '{"title":"hunt #1 meow"}'
